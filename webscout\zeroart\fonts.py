"""
ZeroArt Fonts: Predefined ASCII art fonts
""" 

from typing import Dict, List
from .base import ZeroArtFont

class BlockFont(ZeroArtFont):
    """Block-style ASCII art font"""
    def __init__(self) -> None:
        super().__init__("block")
        self.letters: Dict[str, List[str]] = {
            ' ': ["   ", "   ", "   ", "   ", "   "]
        }
        self._populate_letters()

    def _populate_letters(self) -> None:
        """Populate the font with predefined letters"""
        block_letters = {
            'A': [
                "  █████  ",
                " ██   ██ ",
                " ███████ ",
                " ██   ██ ",
                " ██   ██ "
            ],
            'B': [
                " ██████  ",
                " ██   ██ ",
                " ██████  ",
                " ██   ██ ",
                " ██████  "
            ],
            'C': [
                " ██████  ",
                " ██      ",
                " ██      ",
                " ██      ",
                " ██████  "
            ],
            'D': [
                " ██████  ",
                " ██   ██ ",
                " ██   ██ ",
                " ██   ██ ",
                " ██████  "
            ],
            'E': [
                " ███████ ",
                " ██      ",
                " ██████  ",
                " ██      ",
                " ███████ "
            ],
            'F': [
                " ███████ ",
                " ██      ",
                " ██████  ",
                " ██      ",
                " ██      "
            ],
            'G': [
                " ██████  ",
                " ██      ",
                " ██  ███ ",
                " ██   ██ ",
                " ██████  "
            ],
            'H': [
                " ██   ██ ",
                " ██   ██ ",
                " ███████ ",
                " ██   ██ ",
                " ██   ██ "
            ],
            'I': [
                " ███████ ",
                "   ██   ",
                "   ██   ",
                "   ██   ",
                " ███████ "
            ],
            'J': [
                "      ██ ",
                "      ██ ",
                "      ██ ",
                " ██   ██ ",
                "  █████  "
            ],
            'K': [
                " ██   ██ ",
                " ██  ██  ",
                " ████   ",
                " ██  ██  ",
                " ██   ██ "
            ],
            'L': [
                " ██      ",
                " ██      ",
                " ██      ",
                " ██      ",
                " ███████ "
            ],
            'M': [
                " ██   ██ ",
                " ███ ███ ",
                " ██ █ ██ ",
                " ██   ██ ",
                " ██   ██ "
            ],
            'N': [
                " ██   ██ ",
                " ███  ██ ",
                " ██ █ ██ ",
                " ██  ███ ",
                " ██   ██ "
            ],
            'O': [
                "  █████  ",
                " ██   ██ ",
                " ██   ██ ",
                " ██   ██ ",
                "  █████  "
            ],
            'P': [
                " ██████  ",
                " ██   ██ ",
                " ██████  ",
                " ██      ",
                " ██      "
            ],
            'Q': [
                "  █████  ",
                " ██   ██ ",
                " ██   ██ ",
                "  ██ ██  ",
                "  ██████ "
            ],
            'R': [
                " ██████  ",
                " ██   ██ ",
                " ██████  ",
                " ██  ██  ",
                " ██   ██ "
            ],
            'S': [
                " ██████  ",
                " ██      ",
                " ██████  ",
                "      ██ ",
                " ██████  "
            ],
            'T': [
                " ███████ ",
                "   ██   ",
                "   ██   ",
                "   ██   ",
                "   ██   "
            ],
            'U': [
                " ██   ██ ",
                " ██   ██ ",
                " ██   ██ ",
                " ██   ██ ",
                "  █████  "
            ],
            'V': [
                " ██   ██ ",
                " ██   ██ ",
                " ██   ██ ",
                "  ██ ██  ",
                "   ███   "
            ],
            'W': [
                " ██   ██ ",
                " ██   ██ ",
                " ██ █ ██ ",
                " ███ ███ ",
                "  ██ ██  "
            ],
            'X': [
                " ██   ██ ",
                "  ██ ██  ",
                "   ███   ",
                "  ██ ██  ",
                " ██   ██ "
            ],
            'Y': [
                " ██   ██ ",
                "  ██ ██  ",
                "   ███   ",
                "   ███   ",
                "   ███   "
            ],
            'Z': [
                " ███████ ",
                "     ██  ",
                "    ██   ",
                "   ██    ",
                " ███████ "
            ]
        }
        
        self.letters.update(block_letters)

class SlantFont(ZeroArtFont):
    """Slanted ASCII art font"""
    def __init__(self) -> None:
        super().__init__("slant")
        self.letters: Dict[str, List[str]] = {
            ' ': ["   ", "   ", "   ", "   ", "   "]
        }
        self._populate_letters()

    def _populate_letters(self) -> None:
        """Populate the font with predefined letters"""
        slant_letters = {
            'A': [
                "    /\\    ",
                "   /  \\   ",
                "  / /\\ \\  ",
                " / ____ \\ ",
                "/_/    \\_\\"
            ],
            'B': [
                " ____  ",
                "|  _ \\ ",
                "| |_) |",
                "|  _ < ",
                "| |_) |",
                "|____/ "
            ],
            'C': [
                "  _____  ",
                " / ____|  ",
                "| |       ",
                "| |       ",
                " \\_____|  "
            ],
            'D': [
                " _____   ",
                "|  __ \\  ",
                "| |  | | ",
                "| |  | | ",
                "| |__| | ",
                "|_____/  "
            ],
            'E': [
                " ______  ",
                "|  ____|  ",
                "| |__     ",
                "|  __|    ",
                "| |____   ",
                "|______|  "
            ],
            'F': [
                " ______  ",
                "|  ____|  ",
                "| |__     ",
                "|  __|    ",
                "| |       ",
                "|_|       "
            ],
            'G': [
                "  _____  ",
                " / ____|  ",
                "| |  __   ",
                "| | |_ |  ",
                "| |__| |  ",
                " \\_____|  "
            ],
            'H': [
                " _    _  ",
                "| |  | | ",
                "| |__| | ",
                "|  __  | ",
                "|_|  |_| "
            ],
            'I': [
                " _____ ",
                "|_   _|",
                "  | |  ",
                "  | |  ",
                " _| |_ ",
                "|_____|"
            ],
            'J': [
                "    ___ ",
                "   |_  |",
                "     | |",
                " _   | |",
                "| |__| |",
                " \\____/ "
            ],
            'K': [
                " _  __ ",
                "| |/ / ",
                "| ' /  ",
                "|  <   ",
                "| . \\  ",
                "|_|\\_\\ "
            ],
            'L': [
                " _      ",
                "| |     ",
                "| |     ",
                "| |     ",
                "| |____ ",
                "|______|"
            ],
            'M': [
                " __  __ ",
                "|  \\/  |",
                "| |\\/| |",
                "| |  | |",
                "|_|  |_|",
                "        "
            ],
            'N': [
                " _   _ ",
                "| \\ | |",
                "|  \\| |",
                "| . ` |",
                "| |\\  |",
                "|_| \\_|"
            ],
            'O': [
                "  _____  ",
                " / ____|  ",
                "| |  __   ",
                "| | |_ |  ",
                "| |__| |  ",
                " \\_____|  "
            ],
            'P': [
                " _____   ",
                "|  __ \\  ",
                "| |__) | ",
                "|  ___/  ",
                "| |      ",
                "|_|      "
            ],
            'Q': [
                "  _____  ",
                " / ____|  ",
                "| |  __   ",
                "| | |_ |  ",
                "| |__| |  ",
                " \\____/\\ "
            ],
            'R': [
                " _____   ",
                "|  __ \\  ",
                "| |__) | ",
                "|  _  /  ",
                "| | \\ \\  ",
                "|_|  \\_\\ "
            ],
            'S': [
                "  _____  ",
                " / ____|  ",
                "| (___    ",
                " \\___ \\   ",
                " ____) |  ",
                "|_____/   "
            ],
            'T': [
                " ______  ",
                "|_   _|  ",
                "  | |    ",
                "  | |    ",
                "  | |    ",
                "  |_|    "
            ],
            'U': [
                " _    _  ",
                "| |  | | ",
                "| |  | | ",
                "| |  | | ",
                "| |__| | ",
                " \\____/  "
            ],
            'V': [
                " __     __ ",
                " \\ \\   / / ",
                "  \\ \\_/ /  ",
                "   \\   /   ",
                "    \\_/    "
            ],
            'W': [
                " __    __  ",
                " \\ \\  / /  ",
                "  \\ \\/ /   ",
                "   \\  /    ",
                "    \\/     "
            ],
            'X': [
                " __  __ ",
                " \\ \\/ / ",
                "  \\  /  ",
                "  /  \\  ",
                " /_/\\_\\ "
            ],
            'Y': [
                " _     _ ",
                "| |   | |",
                "| |   | |",
                "| |   | |",
                "|_|   |_|"
            ],
            'Z': [
                " ______  ",
                "|__  / | ",
                "  / /| | ",
                " / /_| | ",
                "/____|_| "
            ]
        }
        
        self.letters.update(slant_letters)

# Add more custom fonts here
class NeonFont(ZeroArtFont):
    """Neon-style ASCII art font"""
    def __init__(self) -> None:
        super().__init__("neon")
        self.letters: Dict[str, List[str]] = {
            ' ': ["   ", "   ", "   ", "   ", "   "]
        }
        self._populate_letters()

    def _populate_letters(self) -> None:
        """Populate neon-style letters"""
        neon_letters = {
            'A': [
                "  ░█████░  ",
                " ██░░░░██ ",
                " ████████ ",
                " ██░░░░██ ",
                " ██░░░░██ "
            ],
            'B': [
                " ████████ ",
                " ██░░░░██ ",
                " ████████ ",
                " ██░░░░██ ",
                " ████████ "
            ],
            'C': [
                "  ░██████ ",
                " ██░░░░   ",
                " ██       ",
                " ██░░░░   ",
                "  ░██████ "
            ],
            'D': [
                " ████████ ",
                " ██░░░░██ ",
                " ██░░░░██ ",
                " ██░░░░██ ",
                " ████████ "
            ],
            'E': [
                " ████████ ",
                " ██░░░░   ",
                " ████████ ",
                " ██░░░░   ",
                " ████████ "
            ],
            'F': [
                " ████████ ",
                " ██░░░░   ",
                " ████████ ",
                " ██░░░░   ",
                " ██░░░░   "
            ],
            'G': [
                "  ░██████ ",
                " ██░░░░   ",
                " ██  ████ ",
                " ██░░░░██ ",
                "  ░██████ "
            ],
            'H': [
                " ██░░░░██ ",
                " ██░░░░██ ",
                " ████████ ",
                " ██░░░░██ ",
                " ██░░░░██ "
            ],
            'I': [
                " ████████ ",
                "   ██░░   ",
                "   ██░░   ",
                "   ██░░   ",
                " ████████ "
            ],
            'J': [
                "      ██░ ",
                "      ██░ ",
                "      ██░ ",
                " ██░░░░██ ",
                "  ██████  "
            ],
            'K': [
                " ██░░░░██ ",
                " ██░░██░  ",
                " ████░░   ",
                " ██░░██░  ",
                " ██░░░░██ "
            ],
            'L': [
                " ██░░░░   ",
                " ██░░░░   ",
                " ██░░░░   ",
                " ██░░░░   ",
                " ████████ "
            ],
            'M': [
                " ██░░░░██ ",
                " ███░░███ ",
                " ██░█░███ ",
                " ██░░░░██ ",
                " ██░░░░██ "
            ],
            'N': [
                " ██░░░░██ ",
                " ███░░░██ ",
                " ██░█░░██ ",
                " ██░░█░██ ",
                " ██░░░███ "
            ],
            'O': [
                "  ██████  ",
                " ██░░░░██ ",
                " ██░░░░██ ",
                " ██░░░░██ ",
                "  ██████  "
            ],
            'P': [
                " ████████ ",
                " ██░░░░██ ",
                " ████████ ",
                " ██░░░░   ",
                " ██░░░░   "
            ],
            'Q': [
                "  ██████  ",
                " ██░░░░██ ",
                " ██░░░░██ ",
                " ██░░██░  ",
                "  ██████░ "
            ],
            'R': [
                " ████████ ",
                " ██░░░░██ ",
                " ████████ ",
                " ██░░██░  ",
                " ██░░░░██ "
            ],
            'S': [
                "  ██████░ ",
                " ██░░░░   ",
                "  ██████  ",
                "     ░░██ ",
                " ████████ "
            ],
            'T': [
                " ████████ ",
                "   ██░░   ",
                "   ██░░   ",
                "   ██░░   ",
                "   ██░░   "
            ],
            'U': [
                " ██░░░░██ ",
                " ██░░░░██ ",
                " ██░░░░██ ",
                " ██░░░░██ ",
                "  ██████  "
            ],
            'V': [
                " ██░░░░██ ",
                " ██░░░░██ ",
                " ██░░░░██ ",
                "  ██░░██  ",
                "   ████   "
            ],
            'W': [
                " ██░░░░██ ",
                " ██░░░░██ ",
                " ██░█░░██ ",
                " ███░███░ ",
                "  ██░░██  "
            ],
            'X': [
                " ██░░░░██ ",
                "  ██░░██  ",
                "   ████   ",
                "  ██░░██  ",
                " ██░░░░██ "
            ],
            'Y': [
                " ██░░░░██ ",
                "  ██░░██  ",
                "   ████   ",
                "   ██░░   ",
                "   ██░░   "
            ],
            'Z': [
                " ████████ ",
                "     ██░  ",
                "    ██░   ",
                "   ██░    ",
                " ████████ "
            ]
        }
        
        self.letters.update(neon_letters)

class CyberFont(ZeroArtFont):
    """Cyberpunk-style ASCII art font"""
    def __init__(self) -> None:
        super().__init__("cyber")
        self.letters: Dict[str, List[str]] = {
            ' ': ["   ", "   ", "   ", "   ", "   "]
        }
        self._populate_letters()

    def _populate_letters(self) -> None:
        """Populate cyberpunk-style letters"""
        cyber_letters = {
            'A': [
                "  ▓█▓▓▓▓  ",
                " ▓▓   ▓▓ ",
                " ▓▓▓▓▓▓▓ ",
                " ▓▓   ▓▓ ",
                " ▓▓   ▓▓ "
            ],
            'B': [
                " ▓▓▓▓▓▓▓ ",
                " ▓▓   ▓▓ ",
                " ▓▓▓▓▓▓▓ ",
                " ▓▓   ▓▓ ",
                " ▓▓▓▓▓▓▓ "
            ],
            'C': [
                " ▓▓▓▓▓▓▓ ",
                " ▓▓      ",
                " ▓▓      ",
                " ▓▓      ",
                " ▓▓▓▓▓▓▓ "
            ],
            'D': [
                " ▓▓▓▓▓▓  ",
                " ▓▓   ▓▓ ",
                " ▓▓   ▓▓ ",
                " ▓▓   ▓▓ ",
                " ▓▓▓▓▓▓  "
            ],
            'E': [
                " ▓▓▓▓▓▓▓ ",
                " ▓▓      ",
                " ▓▓▓▓▓▓  ",
                " ▓▓      ",
                " ▓▓▓▓▓▓▓ "
            ],
            'F': [
                " ▓▓▓▓▓▓▓ ",
                " ▓▓      ",
                " ▓▓▓▓▓▓  ",
                " ▓▓      ",
                " ▓▓      "
            ],
            'G': [
                " ▓▓▓▓▓▓▓ ",
                " ▓▓      ",
                " ▓▓  ▓▓▓ ",
                " ▓▓   ▓▓ ",
                " ▓▓▓▓▓▓▓ "
            ],
            'H': [
                " ▓▓   ▓▓ ",
                " ▓▓   ▓▓ ",
                " ▓▓▓▓▓▓▓ ",
                " ▓▓   ▓▓ ",
                " ▓▓   ▓▓ "
            ],
            'I': [
                " ▓▓▓▓▓▓▓ ",
                "   ▓▓    ",
                "   ▓▓    ",
                "   ▓▓    ",
                " ▓▓▓▓▓▓▓ "
            ],
            'J': [
                "     ▓▓  ",
                "     ▓▓  ",
                "     ▓▓  ",
                " ▓▓  ▓▓  ",
                " ▓▓▓▓▓   "
            ],
            'K': [
                " ▓▓   ▓▓ ",
                " ▓▓  ▓▓  ",
                " ▓▓▓▓▓   ",
                " ▓▓  ▓▓  ",
                " ▓▓   ▓▓ "
            ],
            'L': [
                " ▓▓      ",
                " ▓▓      ",
                " ▓▓      ",
                " ▓▓      ",
                " ▓▓▓▓▓▓▓ "
            ],
            'M': [
                " ▓▓   ▓▓ ",
                " ▓▓▓ ▓▓▓ ",
                " ▓▓▓▓▓▓▓ ",
                " ▓▓ ▓ ▓▓ ",
                " ▓▓   ▓▓ "
            ],
            'N': [
                " ▓▓   ▓▓ ",
                " ▓▓▓  ▓▓ ",
                " ▓▓ ▓ ▓▓ ",
                " ▓▓  ▓▓▓ ",
                " ▓▓   ▓▓ "
            ],
            'O': [
                " ▓▓▓▓▓▓▓ ",
                " ▓▓   ▓▓ ",
                " ▓▓   ▓▓ ",
                " ▓▓   ▓▓ ",
                " ▓▓▓▓▓▓▓ "
            ],
            'P': [
                " ▓▓▓▓▓▓▓ ",
                " ▓▓   ▓▓ ",
                " ▓▓▓▓▓▓▓ ",
                " ▓▓      ",
                " ▓▓      "
            ],
            'Q': [
                " ▓▓▓▓▓▓  ",
                " ▓▓   ▓▓ ",
                " ▓▓   ▓▓ ",
                " ▓▓  ▓▓▓ ",
                " ▓▓▓▓▓ ▓ "
            ],
            'R': [
                " ▓▓▓▓▓▓  ",
                " ▓▓   ▓▓ ",
                " ▓▓▓▓▓▓  ",
                " ▓▓  ▓▓  ",
                " ▓▓   ▓▓ "
            ],
            'S': [
                " ▓▓▓▓▓▓▓ ",
                " ▓▓      ",
                " ▓▓▓▓▓▓▓ ",
                "      ▓▓ ",
                " ▓▓▓▓▓▓▓ "
            ],
            'T': [
                " ▓▓▓▓▓▓▓ ",
                "   ▓▓    ",
                "   ▓▓    ",
                "   ▓▓    ",
                "   ▓▓    "
            ],
            'U': [
                " ▓▓   ▓▓ ",
                " ▓▓   ▓▓ ",
                " ▓▓   ▓▓ ",
                " ▓▓   ▓▓ ",
                " ▓▓▓▓▓▓▓ "
            ],
            'V': [
                " ▓▓   ▓▓ ",
                " ▓▓   ▓▓ ",
                " ▓▓   ▓▓ ",
                "  ▓▓ ▓▓  ",
                "   ▓▓▓   "
            ],
            'W': [
                " ▓▓   ▓▓ ",
                " ▓▓   ▓▓ ",
                " ▓▓ ▓ ▓▓ ",
                " ▓▓▓ ▓▓▓ ",
                "  ▓▓ ▓▓  "
            ],
            'X': [
                " ▓▓   ▓▓ ",
                "  ▓▓ ▓▓  ",
                "   ▓▓▓   ",
                "  ▓▓ ▓▓  ",
                " ▓▓   ▓▓ "
            ],
            'Y': [
                " ▓▓   ▓▓ ",
                "  ▓▓ ▓▓ ",
                "   ▓▓   ",
                "   ▓▓   ",
                "   ▓▓   "
            ],
            'Z': [
                " ▓▓▓▓▓▓▓ ",
                "     ▓▓  ",
                "    ▓▓   ",
                "   ▓▓    ",
                " ▓▓▓▓▓▓▓ "
            ]
        }
        
        self.letters.update(cyber_letters)

class DottedFont(ZeroArtFont):
    """Dotted-style ASCII art font"""
    def __init__(self) -> None:
        super().__init__("dotted")
        self.letters: Dict[str, List[str]] = {
            ' ': ["   ", "   ", "   ", "   ", "   "]
        }
        self._populate_letters()

    def _populate_letters(self) -> None:
        """Populate dotted-style letters"""
        dotted_letters = {
            'A': [
                " ........ ",
                " ::    :: ",
                " :::::::: ",
                " ::    :: ",
                " ::    :: "
            ],
            'B': [
                " ::::::: ",
                " ::    :: ",
                " ::::::: ",
                " ::    :: ",
                " ::::::: "
            ],
            'C': [
                " ::::::: ",
                " ::      ",
                " ::      ",
                " ::      ",
                " ::::::: "
            ],
            'D': [
                " ::::::: ",
                " ::    :: ",
                " ::    :: ",
                " ::    :: ",
                " ::::::: "
            ],
            'E': [
                " :::::::: ",
                " ::       ",
                " ::::::   ",
                " ::       ",
                " :::::::: "
            ],
            'F': [
                " :::::::: ",
                " ::       ",
                " ::::::   ",
                " ::       ",
                " ::       "
            ],
            'G': [
                " ::::::: ",
                " ::      ",
                " :: :::: ",
                " ::   :: ",
                " ::::::: "
            ],
            'H': [
                " ::    :: ",
                " ::    :: ",
                " :::::::: ",
                " ::    :: ",
                " ::    :: "
            ],
            'I': [
                " :::::::: ",
                "    ::    ",
                "    ::    ",
                "    ::    ",
                " :::::::: "
            ],
            'J': [
                "       :: ",
                "       :: ",
                "       :: ",
                " ::    :: ",
                "  ::::::  "
            ],
            'K': [
                " ::    :: ",
                " ::   ::  ",
                " :::::    ",
                " ::  ::   ",
                " ::    :: "
            ],
            'L': [
                " ::       ",
                " ::       ",
                " ::       ",
                " ::       ",
                " :::::::: "
            ],
            'M': [
                " ::    :: ",
                " :::  ::: ",
                " :: :: :: ",
                " ::    :: ",
                " ::    :: "
            ],
            'N': [
                " ::    :: ",
                " :::   :: ",
                " :: :: :: ",
                " ::  :::: ",
                " ::    :: "
            ],
            'O': [
                "  ::::::  ",
                " ::    :: ",
                " ::    :: ",
                " ::    :: ",
                "  ::::::  "
            ],
            'P': [
                " ::::::: ",
                " ::    :: ",
                " ::::::: ",
                " ::       ",
                " ::       "
            ],
            'Q': [
                "  ::::::  ",
                " ::    :: ",
                " ::    :: ",
                " ::  :::: ",
                "  ::::  : "
            ],
            'R': [
                " ::::::: ",
                " ::    :: ",
                " ::::::: ",
                " ::  ::   ",
                " ::    :: "
            ],
            'S': [
                " ::::::: ",
                " ::      ",
                " ::::::: ",
                "      :: ",
                " ::::::: "
            ],
            'T': [
                " :::::::: ",
                "    ::    ",
                "    ::    ",
                "    ::    ",
                "    ::    "
            ],
            'U': [
                " ::    :: ",
                " ::    :: ",
                " ::    :: ",
                " ::    :: ",
                "  ::::::  "
            ],
            'V': [
                " ::    :: ",
                " ::    :: ",
                " ::    :: ",
                "  ::  ::  ",
                "    ::    "
            ],
            'W': [
                " ::    :: ",
                " ::    :: ",
                " :: :: :: ",
                " :::  ::: ",
                " ::    :: "
            ],
            'X': [
                " ::    :: ",
                "  ::  ::  ",
                "    ::    ",
                "  ::  ::  ",
                " ::    :: "
            ],
            'Y': [
                " ::    :: ",
                "  ::  ::  ",
                "    ::    ",
                "    ::    ",
                "    ::    "
            ],
            'Z': [
                " :::::::: ",
                "      ::  ",
                "    ::    ",
                "  ::      ",
                " :::::::: "
            ]
        }
        
        self.letters.update(dotted_letters)

class ShadowFont(ZeroArtFont):
    """Shadow-style ASCII art font"""
    def __init__(self) -> None:
        super().__init__("shadow")
        self.letters: Dict[str, List[str]] = {
            ' ': ["   ", "   ", "   ", "   ", "   "]
        }
        self._populate_letters()

    def _populate_letters(self) -> None:
        """Populate shadow-style letters"""
        shadow_letters = {
            'A': [
                "   ██    ",
                "  ████   ",
                " ██  ██  ",
                "████████ ",
                "██    ██▓",
                "       ▓▓"
            ],
            'B': [
                "██████   ",
                "██   ██  ",
                "███████  ",
                "██    ██ ",
                "███████  ",
                " ▓▓▓▓▓▓▓ "
            ],
            'C': [
                " ██████  ",
                "██    ██ ",
                "██       ",
                "██    ██ ",
                " ██████  ",
                "  ▓▓▓▓▓▓ "
            ],
            'D': [
                "██████   ",
                "██   ██  ",
                "██    ██ ",
                "██    ██ ",
                "██████   ",
                "▓▓▓▓▓▓   "
            ],
            'E': [
                "████████ ",
                "██       ",
                "██████   ",
                "██       ",
                "████████ ",
                "▓▓▓▓▓▓▓▓ "
            ],
            'F': [
                "████████ ",
                "██       ",
                "██████   ",
                "██       ",
                "██       ",
                "▓▓       "
            ],
            'G': [
                " ██████  ",
                "██       ",
                "██   ███ ",
                "██    ██ ",
                " ██████  ",
                "  ▓▓▓▓▓▓ "
            ],
            'H': [
                "██    ██ ",
                "██    ██ ",
                "████████ ",
                "██    ██ ",
                "██    ██ ",
                "▓▓    ▓▓ "
            ],
            'I': [
                "████████ ",
                "   ██    ",
                "   ██    ",
                "   ██    ",
                "████████ ",
                "▓▓▓▓▓▓▓▓ "
            ],
            'J': [
                "     ██  ",
                "     ██  ",
                "     ██  ",
                "██   ██  ",
                " █████   ",
                "  ▓▓▓▓   "
            ],
            'K': [
                "██   ██  ",
                "██  ██   ",
                "█████    ",
                "██  ██   ",
                "██   ██  ",
                "▓▓   ▓▓  "
            ],
            'L': [
                "██       ",
                "██       ",
                "██       ",
                "██       ",
                "████████ ",
                "▓▓▓▓▓▓▓▓ "
            ],
            'M': [
                "██    ██ ",
                "███  ███ ",
                "████████ ",
                "██ ██ ██ ",
                "██    ██ ",
                "▓▓    ▓▓ "
            ],
            'N': [
                "██    ██ ",
                "███   ██ ",
                "████  ██ ",
                "██ ██ ██ ",
                "██   ███ ",
                "▓▓    ▓▓ "
            ],
            'O': [
                " ██████  ",
                "██    ██ ",
                "██    ██ ",
                "██    ██ ",
                " ██████  ",
                "  ▓▓▓▓▓▓ "
            ],
            'P': [
                "███████  ",
                "██    ██ ",
                "███████  ",
                "██       ",
                "██       ",
                "▓▓       "
            ],
            'Q': [
                " ██████  ",
                "██    ██ ",
                "██    ██ ",
                "██ ▄▄ ██ ",
                " ██████  ",
                "  ▓▓▓▓▓▓▓"
            ],
            'R': [
                "███████  ",
                "██    ██ ",
                "███████  ",
                "██   ██  ",
                "██    ██ ",
                "▓▓    ▓▓ "
            ],
            'S': [
                " ██████  ",
                "██       ",
                " ██████  ",
                "      ██ ",
                "███████  ",
                "▓▓▓▓▓▓▓  "
            ],
            'T': [
                "████████ ",
                "   ██    ",
                "   ██    ",
                "   ██    ",
                "   ██    ",
                "   ▓▓    "
            ],
            'U': [
                "██    ██ ",
                "██    ██ ",
                "██    ██ ",
                "██    ██ ",
                " ██████  ",
                "  ▓▓▓▓▓▓ "
            ],
            'V': [
                "██    ██ ",
                "██    ██ ",
                "██    ██ ",
                " ██  ██  ",
                "  ████   ",
                "   ▓▓    "
            ],
            'W': [
                "██    ██ ",
                "██    ██ ",
                "██ ██ ██ ",
                "████████ ",
                " ██  ██  ",
                "  ▓  ▓▓  "
            ],
            'X': [
                "██    ██ ",
                " ██  ██  ",
                "  ████   ",
                " ██  ██  ",
                "██    ██ ",
                "▓▓    ▓▓ "
            ],
            'Y': [
                "██    ██ ",
                " ██  ██  ",
                "  ████   ",
                "   ██    ",
                "   ██    ",
                "   ▓▓    "
            ],
            'Z': [
                "████████ ",
                "     ██  ",
                "   ██    ",
                " ██      ",
                "████████ ",
                "▓▓▓▓▓▓▓▓ "
            ]
        }
        
        self.letters.update(shadow_letters)