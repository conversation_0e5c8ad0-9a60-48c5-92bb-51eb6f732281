<div align="center">
  <a href="https://github.com/OEvortex/Webscout">
    <img src="https://img.shields.io/badge/YTToolkit-YouTube%20Data%20API-red?style=for-the-badge&logo=youtube&logoColor=white" alt="YTToolkit Logo">
  </a>
  <h1>YouTube Data API</h1>
  <p><strong>Extract YouTube data without official API keys</strong></p>
</div>

> [!NOTE]
> This documentation has been merged into the main [YTToolkit README](../README.md).
> Please refer to the main documentation for comprehensive information about all YTToolkit features.

## Quick Links

- [Installation & Setup](../README.md#-installation)
- [Channel Information](../README.md#channel-information)
- [Video Metadata](../README.md#video-metadata)
- [Search & Trending](../README.md#search--trending)
- [Detailed Documentation](../README.md#-detailed-documentation)

## Module Overview

| Module | File | Description |
|--------|------|-------------|
| **Channel** | [`channel.py`](channel.py) | Channel metadata and interaction |
| **Video** | [`video.py`](video.py) | Video information extraction |
| **Search** | [`query.py`](query.py) | Advanced search capabilities |
| **Extras** | [`extras.py`](extras.py) | Trending and category-based video retrieval |
| **Playlist** | [`playlist.py`](playlist.py) | Playlist metadata extraction |

## Key Features

- **No API Key Required**: Extract YouTube data without official API keys
- **Channel Information**: Subscribers, views, description, avatars, banners
- **Video Metadata**: Title, views, duration, thumbnails, embed codes
- **Search Capabilities**: Find videos, channels, and playlists
- **Trending Content**: Access trending videos across categories

<div align="center">
  <p>
    <a href="https://t.me/PyscoutAI"><img alt="Telegram Group" src="https://img.shields.io/badge/Telegram%20Group-2CA5E0?style=for-the-badge&logo=telegram&logoColor=white"></a>
    <a href="https://youtube.com/@OEvortex"><img alt="YouTube" src="https://img.shields.io/badge/YouTube-FF0000?style=for-the-badge&logo=youtube&logoColor=white"></a>
  </p>
</div>
