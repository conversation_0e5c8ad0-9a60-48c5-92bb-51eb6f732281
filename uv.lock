# This file was autogenerated by uv via the following command:
#    uv pip compile --all-extras --output-file uv.lock pyproject.toml
aiofiles==24.1.0
    # via webscout (pyproject.toml)
aiohappyeyeballs==2.6.1
    # via aiohttp
aiohttp==3.12.2
    # via webscout (pyproject.toml)
aiosignal==1.3.2
    # via aiohttp
annotated-types==0.7.0
    # via pydantic
anyio==4.9.0
    # via
    #   httpx
    #   openai
    #   starlette
    #   watchfiles
attrs==25.3.0
    # via aiohttp
beautifulsoup4==4.13.4
    # via markdownify
brotli==1.1.0
    # via webscout (pyproject.toml)
bson==0.5.10
    # via webscout (pyproject.toml)
cachetools==5.5.2
    # via google-auth
certifi==2025.4.26
    # via
    #   curl-cffi
    #   httpcore
    #   httpx
    #   requests
cffi==1.17.1
    # via curl-cffi
charset-normalizer==3.4.2
    # via requests
click==8.2.1
    # via uvicorn
cloudscraper==1.2.71
    # via webscout (pyproject.toml)
colorama==0.4.6
    # via
    #   webscout (pyproject.toml)
    #   click
    #   pytest
    #   tqdm
    #   uvicorn
curl-cffi==0.11.1
    # via webscout (pyproject.toml)
deprecated==1.2.18
    # via nodriver
distro==1.9.0
    # via openai
fastapi==0.115.12
    # via webscout (pyproject.toml)
filelock==3.18.0
    # via huggingface-hub
frozenlist==1.6.0
    # via
    #   aiohttp
    #   aiosignal
fsspec==2025.5.1
    # via
    #   gradio-client
    #   huggingface-hub
google-ai-generativelanguage==0.6.15
    # via google-generativeai
google-api-core==2.24.2
    # via
    #   google-ai-generativelanguage
    #   google-api-python-client
    #   google-generativeai
google-api-python-client==2.170.0
    # via google-generativeai
google-auth==2.40.2
    # via
    #   google-ai-generativelanguage
    #   google-api-core
    #   google-api-python-client
    #   google-auth-httplib2
    #   google-generativeai
google-auth-httplib2==0.2.0
    # via google-api-python-client
google-generativeai==0.8.5
    # via webscout (pyproject.toml)
googleapis-common-protos==1.70.0
    # via
    #   google-api-core
    #   grpcio-status
gradio-client==1.10.1
    # via webscout (pyproject.toml)
grpcio==1.71.0
    # via
    #   google-api-core
    #   grpcio-status
grpcio-status==1.71.0
    # via google-api-core
h11==0.16.0
    # via
    #   httpcore
    #   uvicorn
html5lib==1.1
    # via webscout (pyproject.toml)
httpcore==1.0.9
    # via httpx
httplib2==0.22.0
    # via
    #   google-api-python-client
    #   google-auth-httplib2
httptools==0.6.4
    # via uvicorn
httpx==0.28.1
    # via
    #   gradio-client
    #   ollama
    #   openai
huggingface-hub==0.32.2
    # via gradio-client
idna==3.10
    # via
    #   anyio
    #   httpx
    #   requests
    #   yarl
iniconfig==2.1.0
    # via pytest
jiter==0.10.0
    # via openai
lxml==5.4.0
    # via webscout (pyproject.toml)
markdown-it-py==3.0.0
    # via rich
markdownify==1.1.0
    # via webscout (pyproject.toml)
mdurl==0.1.2
    # via markdown-it-py
mistune==3.1.3
    # via webscout (pyproject.toml)
mss==10.0.0
    # via nodriver
multidict==6.4.4
    # via
    #   aiohttp
    #   yarl
nest-asyncio==1.6.0
    # via webscout (pyproject.toml)
nodriver==0.46.1
    # via webscout (pyproject.toml)
ollama==0.4.9
    # via webscout (pyproject.toml)
openai==1.82.0
    # via webscout (pyproject.toml)
orjson==3.10.18
    # via webscout (pyproject.toml)
packaging==25.0
    # via
    #   gradio-client
    #   huggingface-hub
    #   pytest
pillow==11.2.1
    # via webscout (pyproject.toml)
pip==25.1.1
    # via webscout (pyproject.toml)
pluggy==1.6.0
    # via pytest
propcache==0.3.1
    # via
    #   aiohttp
    #   yarl
proto-plus==1.26.1
    # via
    #   google-ai-generativelanguage
    #   google-api-core
protobuf==5.29.4
    # via
    #   google-ai-generativelanguage
    #   google-api-core
    #   google-generativeai
    #   googleapis-common-protos
    #   grpcio-status
    #   proto-plus
psutil==7.0.0
    # via webscout (pyproject.toml)
pyasn1==0.6.1
    # via
    #   pyasn1-modules
    #   rsa
pyasn1-modules==0.4.2
    # via google-auth
pycparser==2.22
    # via cffi
pydantic==2.11.5
    # via
    #   webscout (pyproject.toml)
    #   fastapi
    #   google-generativeai
    #   ollama
    #   openai
pydantic-core==2.33.2
    # via pydantic
pygments==2.19.1
    # via rich
pyparsing==3.2.3
    # via
    #   cloudscraper
    #   httplib2
pytest==8.3.5
    # via webscout (pyproject.toml)
python-dateutil==2.9.0.post0
    # via bson
python-dotenv==1.1.0
    # via uvicorn
python-multipart==0.0.20
    # via webscout (pyproject.toml)
pyyaml==6.0.2
    # via
    #   webscout (pyproject.toml)
    #   huggingface-hub
    #   uvicorn
regex==2024.11.6
    # via tiktoken
requests==2.32.3
    # via
    #   webscout (pyproject.toml)
    #   cloudscraper
    #   google-api-core
    #   huggingface-hub
    #   requests-toolbelt
    #   tiktoken
requests-toolbelt==1.0.0
    # via cloudscraper
rich==14.0.0
    # via webscout (pyproject.toml)
rsa==4.9.1
    # via google-auth
ruff==0.11.11
    # via webscout (pyproject.toml)
setuptools==80.9.0
    # via webscout (pyproject.toml)
six==1.17.0
    # via
    #   bson
    #   html5lib
    #   markdownify
    #   python-dateutil
sniffio==1.3.1
    # via
    #   anyio
    #   openai
soupsieve==2.7
    # via beautifulsoup4
starlette==0.46.2
    # via fastapi
tiktoken==0.9.0
    # via webscout (pyproject.toml)
tqdm==4.67.1
    # via
    #   google-generativeai
    #   huggingface-hub
    #   openai
typing-extensions==4.13.2
    # via
    #   anyio
    #   beautifulsoup4
    #   fastapi
    #   google-generativeai
    #   gradio-client
    #   huggingface-hub
    #   openai
    #   pydantic
    #   pydantic-core
    #   typing-inspection
typing-inspection==0.4.1
    # via pydantic
uritemplate==4.1.1
    # via google-api-python-client
urllib3==2.4.0
    # via requests
uvicorn==0.34.2
    # via webscout (pyproject.toml)
watchfiles==1.0.5
    # via uvicorn
webencodings==0.5.1
    # via html5lib
websocket-client==1.8.0
    # via webscout (pyproject.toml)
websockets==15.0.1
    # via
    #   gradio-client
    #   nodriver
    #   uvicorn
wheel==0.45.1
    # via webscout (pyproject.toml)
wrapt==1.17.2
    # via deprecated
yarl==1.20.0
    # via aiohttp
zstandard==0.23.0
    # via webscout (pyproject.toml)
