version: 2
updates:
  # Maintain dependencies for Python packages
  - package-ecosystem: "pip"
    directory: "/"
    schedule:
      interval: "weekly"
    open-pull-requests-limit: 10
    labels:
      - "dependencies"
      - "python"
    reviewers:
      - "OEvortex"  # Updated with your correct GitHub username
    commit-message:
      prefix: "pip"
      include: "scope"

  # Maintain dependencies for GitHub Actions
  - package-ecosystem: "github-actions"
    directory: "/"
    schedule:
      interval: "weekly"
    open-pull-requests-limit: 5
    labels:
      - "dependencies"
      - "github-actions"
    commit-message:
      prefix: "github-actions"
      include: "scope"