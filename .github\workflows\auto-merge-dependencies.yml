name: Auto-merge Dependencies

on:
  pull_request:
    branches: [ main, master ]

permissions:
  contents: write
  pull-requests: write

jobs:
  auto-merge:
    runs-on: ubuntu-latest
    if: ${{ github.actor == 'dependabot[bot]' }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up GitHub CLI
        run: |
          sudo apt-get update && sudo apt-get install -y gh

      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.x'
          
      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install .[dev]
          
      - name: Run tests
        run: |
          if [ -d tests ] || ls webscout/Provider/UNFINISHED/test_*.py 2>/dev/null; then \
            pytest || python webscout/Provider/UNFINISHED/test_lmarena.py; \
          else \
            echo "No tests found, skipping tests"; \
          fi
          
      - name: Dependabot metadata
        id: metadata
        uses: dependabot/fetch-metadata@v2
        with:
          github-token: "${{ secrets.GITHUB_TOKEN }}"
          
      - name: Auto-merge patch and minor updates
        if: ${{ steps.metadata.outputs.update-type == 'version-update:semver-minor' || steps.metadata.outputs.update-type == 'version-update:semver-patch' }}
        run: gh pr merge --auto --merge "$PR_URL"
        env:
          PR_URL: ${{ github.event.pull_request.html_url }}
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}